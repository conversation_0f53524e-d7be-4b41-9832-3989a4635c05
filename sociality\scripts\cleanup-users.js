// Script to clean up problematic users from the database
// Run this with: node scripts/cleanup-users.js

import mongoose from "mongoose";
import User from "../backend/models/userModel.js";
import dotenv from "dotenv";

dotenv.config();

const cleanupUsers = async () => {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URI);
        console.log("Connected to MongoDB");

        // Find problematic users
        const problematicUsers = await User.find({
            $or: [
                { username: { $exists: false } },
                { username: null },
                { username: "" },
                { name: { $exists: false } },
                { name: null },
                { name: "" }
            ]
        });

        console.log(`Found ${problematicUsers.length} problematic users:`);
        problematicUsers.forEach(user => {
            console.log(`- ID: ${user._id}, Username: ${user.username}, Name: ${user.name}`);
        });

        if (problematicUsers.length > 0) {
            console.log("\nDo you want to delete these users? (This action cannot be undone)");
            console.log("To proceed, uncomment the deletion code below and run again.");
            
            // Uncomment the lines below to actually delete the problematic users
            // const result = await User.deleteMany({
            //     $or: [
            //         { username: { $exists: false } },
            //         { username: null },
            //         { username: "" },
            //         { name: { $exists: false } },
            //         { name: null },
            //         { name: "" }
            //     ]
            // });
            // console.log(`Deleted ${result.deletedCount} problematic users`);
        } else {
            console.log("No problematic users found!");
        }

        // Check for frozen users
        const frozenUsers = await User.find({ isFrozen: true });
        console.log(`\nFound ${frozenUsers.length} frozen users:`);
        frozenUsers.forEach(user => {
            console.log(`- ID: ${user._id}, Username: ${user.username}, Name: ${user.name}`);
        });

    } catch (error) {
        console.error("Error:", error);
    } finally {
        await mongoose.disconnect();
        console.log("Disconnected from MongoDB");
    }
};

cleanupUsers();
